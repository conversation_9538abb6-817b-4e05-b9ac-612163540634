#!/bin/bash

# Test script pro LIMWRAPP s čekáním mezi příkazy
cd /home/<USER>/vyvoj/limwrapp

echo "=== Spouštím LIMWRAPP test ==="

# Spustíme aplikaci na pozadí
python src/main.py &
APP_PID=$!

# Počkáme na spuštění
sleep 2

# Pošleme příkazy s čekáním
echo "1" > /tmp/limwrapp_input
echo "Poslán příkaz 1 (seznam průzku<PERSON>ů)"
sleep 3

echo "13" >> /tmp/limwrapp_input  
echo "Poslán příkaz 13 (výběr průzkumu 778769)"
sleep 2

echo "2" >> /tmp/limwrapp_input
echo "Poslán příkaz 2 (na<PERSON><PERSON><PERSON><PERSON><PERSON> dat)"
sleep 2

echo "13" >> /tmp/limwrapp_input
echo "Poslán příkaz 13 (potvrz<PERSON><PERSON> průzkumu)"
sleep 15  # <PERSON>š<PERSON> ček<PERSON> na zpracování dat

echo "0" >> /tmp/limwrapp_input
echo "Poslán příkaz 0 (ukončení)"
sleep 2

# Ukončíme aplikaci
kill $APP_PID 2>/dev/null

echo "=== Test dokončen ==="
