"""
Inicializace aplikace - kontrola ad<PERSON>, konfigurace, migrace dat
"""

import os
import logging
import shutil
from typing import Dict, List, Tuple
from config_loader import get_available_servers
from path_manager import path_manager

logger = logging.getLogger(__name__)

class AppInitializer:
    """Třída pro inicializaci aplikace při spuštění"""
    
    def __init__(self):
        self.servers = get_available_servers()
        
    def initialize(self) -> bool:
        """
        Hlavní inicializační funkce
        
        Returns:
            True pokud inicializace proběhla úspěšně
        """
        logger.info("Spouštím inicializaci aplikace...")
        
        try:
            # 1. Kontrola konfigurace
            if not self._validate_config():
                return False
                
            # 2. Vytvořen<PERSON> základních adres<PERSON>
            self._create_base_directories()
            
            # 3. Vytvoření ad<PERSON> pro servery
            self._create_server_directories()
            
            # 4. <PERSON><PERSON><PERSON> star<PERSON>ch dat (pokud existují)
            self._migrate_legacy_data()
            
            # 5. Kontrola integrity dat
            self._validate_data_integrity()
            
            logger.info("✓ Inicializace aplikace dokončena úspěšně")
            return True
            
        except Exception as e:
            logger.error(f"✗ Chyba při inicializaci aplikace: {str(e)}")
            return False
    
    def _validate_config(self) -> bool:
        """Kontrola konfigurace serverů"""
        logger.info("Kontroluji konfiguraci serverů...")
        
        if not self.servers:
            logger.warning("Žádné servery nejsou nakonfigurovány v .env")
            return True  # Pokračujeme s default serverem
            
        # Kontrola povinných polí pro každý server
        required_fields = ['name', 'API_URL', 'USERNAME', 'PASSWORD']
        
        for server_key, server_config in self.servers.items():
            for field in required_fields:
                if not server_config.get(field):
                    logger.error(f"Server {server_key} nemá nakonfigurované pole: {field}")
                    return False
                    
            logger.info(f"✓ Server {server_key}: {server_config['name']}")
            
        return True
    
    def _create_base_directories(self) -> None:
        """Vytvoření základních adresářů aplikace"""
        logger.info("Vytvářím základní adresáře...")

        # Získáme root adresář projektu (o úroveň výš než src/)
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        base_dirs = ['data', 'charts', 'logs', 'test']

        for dir_name in base_dirs:
            dir_path = os.path.join(project_root, dir_name)
            if not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)
                logger.info(f"✓ Vytvořen adresář: {dir_path}")
            else:
                logger.debug(f"Adresář již existuje: {dir_path}")
    
    def _create_server_directories(self) -> None:
        """Vytvoření adresářů pro všechny nakonfigurované servery"""
        logger.info("Vytvářím adresáře pro servery...")

        # Získáme root adresář projektu
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

        # Default server
        default_dirs = [
            "data/dotazniky.urad.online",
            "charts/dotazniky.urad.online"
        ]

        for dir_name in default_dirs:
            dir_path = os.path.join(project_root, dir_name)
            if not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)
                logger.info(f"✓ Vytvořen default server adresář: {dir_path}")

        # Nakonfigurované servery
        for server_key, server_config in self.servers.items():
            server_name = server_config['name']

            server_dirs = [
                f"data/{server_name}",
                f"charts/{server_name}"
            ]

            for dir_name in server_dirs:
                dir_path = os.path.join(project_root, dir_name)
                if not os.path.exists(dir_path):
                    os.makedirs(dir_path, exist_ok=True)
                    logger.info(f"✓ Vytvořen server adresář: {dir_path}")
                else:
                    logger.debug(f"Server adresář již existuje: {dir_path}")
    
    def _migrate_legacy_data(self) -> None:
        """Migrace dat ze starých umístění"""
        logger.info("Kontroluji potřebu migrace starých dat...")
        
        legacy_paths = [
            ("src/data", "data/dotazniky.urad.online"),
            ("src/charts", "charts/dotazniky.urad.online")
        ]
        
        for old_path, new_path in legacy_paths:
            if os.path.exists(old_path):
                logger.info(f"Nalezena stará data v: {old_path}")
                self._migrate_directory(old_path, new_path)
    
    def _migrate_directory(self, old_path: str, new_path: str) -> None:
        """Migrace adresáře ze starého umístění"""
        try:
            if not os.path.exists(new_path):
                os.makedirs(new_path, exist_ok=True)
            
            # Přesun všech podadresářů (survey ID)
            for item in os.listdir(old_path):
                old_item_path = os.path.join(old_path, item)
                new_item_path = os.path.join(new_path, item)
                
                if os.path.isdir(old_item_path):
                    if not os.path.exists(new_item_path):
                        shutil.move(old_item_path, new_item_path)
                        logger.info(f"✓ Migrován průzkum: {item}")
                    else:
                        logger.warning(f"Průzkum {item} již existuje v novém umístění")
                        
            # Smazání starého adresáře pokud je prázdný
            if not os.listdir(old_path):
                os.rmdir(old_path)
                logger.info(f"✓ Smazán prázdný starý adresář: {old_path}")
                
        except Exception as e:
            logger.error(f"Chyba při migraci {old_path}: {str(e)}")
    
    def _validate_data_integrity(self) -> None:
        """Kontrola integrity existujících dat"""
        logger.info("Kontroluji integritu dat...")
        
        # Kontrola pro default server
        self._check_server_data("dotazniky.urad.online")
        
        # Kontrola pro nakonfigurované servery
        for server_key, server_config in self.servers.items():
            server_name = server_config['name']
            self._check_server_data(server_name)
    
    def _check_server_data(self, server_name: str) -> None:
        """Kontrola dat pro konkrétní server"""
        data_path = f"data/{server_name}"
        charts_path = f"charts/{server_name}"
        
        if not os.path.exists(data_path):
            return
            
        surveys = [d for d in os.listdir(data_path) 
                  if os.path.isdir(os.path.join(data_path, d)) and d.isdigit()]
        
        if surveys:
            logger.info(f"Server {server_name}: nalezeno {len(surveys)} průzkumů")
            
            # Kontrola základních souborů pro každý průzkum
            for survey_id in surveys:
                survey_path = os.path.join(data_path, survey_id)
                files = os.listdir(survey_path)
                
                has_responses = any(f.startswith('responses') for f in files)
                has_structure = 'structure.lss' in files
                
                if has_responses and has_structure:
                    logger.debug(f"✓ Průzkum {survey_id}: základní soubory OK")
                else:
                    logger.warning(f"⚠ Průzkum {survey_id}: chybí základní soubory")
    
    def get_initialization_report(self) -> Dict[str, any]:
        """Vrátí report o stavu inicializace"""
        report = {
            'servers_configured': len(self.servers),
            'servers': {},
            'total_surveys': 0
        }
        
        # Default server
        default_surveys = self._count_surveys("dotazniky.urad.online")
        report['servers']['default'] = {
            'name': 'dotazniky.urad.online',
            'surveys_count': default_surveys
        }
        report['total_surveys'] += default_surveys
        
        # Nakonfigurované servery
        for server_key, server_config in self.servers.items():
            server_name = server_config['name']
            surveys_count = self._count_surveys(server_name)
            
            report['servers'][server_key] = {
                'name': server_name,
                'surveys_count': surveys_count
            }
            report['total_surveys'] += surveys_count
            
        return report
    
    def _count_surveys(self, server_name: str) -> int:
        """Spočítá průzkumy pro server"""
        data_path = f"data/{server_name}"
        
        if not os.path.exists(data_path):
            return 0
            
        surveys = [d for d in os.listdir(data_path) 
                  if os.path.isdir(os.path.join(data_path, d)) and d.isdigit()]
        
        return len(surveys)

# Globální instance
app_initializer = AppInitializer()
