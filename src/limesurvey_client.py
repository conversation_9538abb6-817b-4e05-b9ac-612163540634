import requests
import json
import os
from datetime import datetime
from typing import Optional
try:
    from dotenv import load_dotenv
except ImportError:
    def load_dotenv():
        pass
from config_loader import load_config
from logger import get_logger

load_dotenv()
logger = get_logger(__name__)

class LimeSurveyClient:
    def __init__(self, data_dir: str = "data", server_key: str = None):
        """
        Inicializace klienta s konfigurací z .env

        Args:
            data_dir: Adresář pro ukládání stažených dat
            server_key: Klíč serveru (např. 'server1', 'server2') nebo None pro default
        """
        config = load_config()

        if server_key and server_key in config.get('LIMESURVEY_SERVERS', {}):
            # Použití specifického serveru
            server_config = config['LIMESURVEY_SERVERS'][server_key]
            self.api_url = server_config['API_URL']
            self.username = server_config['USERNAME']
            self.password = server_config['PASSWORD']
            self.server_name = server_config['name']
            self.server_key = server_key
        else:
            # Použití defaultního serveru
            self.api_url = config['LIMESURVEY']['API_URL']
            self.username = config['LIMESURVEY']['USERNAME']
            self.password = config['LIMESURVEY']['PASSWORD']
            self.server_name = "dotazniky.urad.online"  # default server name
            self.server_key = "default"

        self.session_key = None
        self.data_dir = data_dir
        
        if not all([self.api_url, self.username, self.password]):
            logger.warning("Chybí konfigurace LimeSurvey v .env, přechod do test módu")
            
        # Vytvoření hlavního adresáře pro data
        os.makedirs(self.data_dir, exist_ok=True)
        logger.info(f"Inicializován LimeSurvey klient (data_dir: {self.data_dir})")
        
    def _make_request(self, method, params):
        """Základní metoda pro volání API s lepším zpracováním chyb"""
        payload = {
            "method": method,
            "params": params,
            "id": 1
        }
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        try:
            response = requests.post(
                self.api_url,
                data=json.dumps(payload),
                headers=headers,
                timeout=30
            )
            
            # Kontrola status kódu
            if response.status_code != 200:
                logger.error(f"API Error: {response.status_code}")
                logger.error(f"Request method: {method}")
                logger.error(f"Request params: {params}")
                logger.error(f"Response: {response.text[:1000]}")
                return None
                
            # Zpracování JSON odpovědi
            try:
                json_data = response.json()
                if json_data.get('error'):
                    logger.error(f"API Error: {json_data['error']}")
                    return None
                
                result = json_data.get('result')
                if isinstance(result, str):
                    try:
                        return json.loads(result)
                    except json.JSONDecodeError:
                        return result
                return result
            except json.JSONDecodeError as e:
                logger.error(f"JSON Decode Error: {str(e)}")
                logger.debug(f"Response Content: {response.text[:500]}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Request Error: {str(e)}")
            return None

    def get_session_key(self):
        """Získání session key pro RPC API pomocí uživatelského jména a hesla"""
        if not self.session_key:
            self.session_key = self._make_request("get_session_key", [self.username, self.password])
        return self.session_key

    def get_responses(self, survey_id: str, completed_only: bool = False,
        start_date: Optional[datetime] = None, end_date: Optional[datetime] = None) -> str:
        """
        Stažení dat průzkumu a uložení do CSV
        
        Args:
            survey_id: ID průzkumu
            completed_only: Pouze dokončené odpovědi
            start_date: Počáteční datum
            end_date: Koncové datum
            
        Returns:
            Cesta k uloženému CSV souboru
        """
        logger.info(f"Stahuji odpovědi pro průzkum {survey_id}")
        
        # Příprava parametrů
        params = [
            self.get_session_key(),
            survey_id,
            'csv',
            None,  # headingType
            'complete' if completed_only else 'all',  # responseType
            'en',  # lang
            True,  # completionStatus
            start_date.strftime("%Y-%m-%d %H:%M:%S") if start_date else None,
            end_date.strftime("%Y-%m-%d %H:%M:%S") if end_date else None
        ]
        
        # Stažení dat
        data = self._make_request("export_responses", params)
        if not data:
            raise ValueError(f"Nepodařilo se stáhnout data pro průzkum {survey_id}")
            
        # Převod dat na string pokud je to dict
        if isinstance(data, dict):
            data = json.dumps(data, ensure_ascii=False)
            
        # Uložení do souboru
        csv_path = os.path.join(self.data_dir, f"survey_{survey_id}_responses.csv")
        with open(csv_path, 'w', encoding='utf-8') as f:
            f.write(str(data))
            
        # Dekomprese a validace CSV
        from data_transformer import decompress_csv, validate_csv_structure
        decompress_csv(csv_path)
        validate_csv_structure(csv_path)
            
        logger.info(f"Data uložena a validována do {csv_path}")
        return csv_path
import requests
import json
import os
from datetime import datetime
from typing import Optional
try:
    try:
        from dotenv import load_dotenv
    except ImportError:
        def load_dotenv():
            pass
except ImportError:
    def load_dotenv():
        pass
from config_loader import load_config
from logger import get_logger

load_dotenv()
logger = get_logger(__name__)

class LimeSurveyClient:
    def __init__(self, data_dir: str = "data"):
        """
        Inicializace klienta s konfigurací z .env
        
        Args:
            data_dir: Adresář pro ukládání stažených dat
        """
        config = load_config()
        self.api_url = config['LIMESURVEY']['API_URL']
        self.username = config['LIMESURVEY']['USERNAME']
        self.password = config['LIMESURVEY']['PASSWORD']
        self.session_key = None
        self.data_dir = data_dir
        
        if not all([self.api_url, self.username, self.password]):
            raise ValueError("Chybí konfigurace LimeSurvey v .env")
            
        # Vytvoření adresáře pro data
        os.makedirs(self.data_dir, exist_ok=True)
        logger.info(f"Inicializován LimeSurvey klient (data_dir: {self.data_dir})")
        
    def _make_request(self, method, params):
        """Základní metoda pro volání API s lepším zpracováním chyb"""
        payload = {
            "method": method,
            "params": params,
            "id": 1
        }
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        try:
            response = requests.post(
                self.api_url,
                data=json.dumps(payload),
                headers=headers,
                timeout=30
            )
            
            # Kontrola status kódu
            if response.status_code != 200:
                logger.error(f"API Error: {response.status_code}")
                logger.debug(f"Response: {response.text[:500]}")
                return None
                
            # Zpracování JSON odpovědi
            try:
                json_data = response.json()
                if json_data.get('error'):
                    logger.error(f"API Error: {json_data['error']}")
                    return None
                
                result = json_data.get('result')
                if isinstance(result, str):
                    try:
                        return json.loads(result)
                    except json.JSONDecodeError:
                        return result
                return result
            except json.JSONDecodeError as e:
                logger.error(f"JSON Decode Error: {str(e)}")
                logger.debug(f"Response Content: {response.text[:500]}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Request Error: {str(e)}")
            return None

    def get_session_key(self):
        """Získání session key pro RPC API pomocí uživatelského jména a hesla"""
        if not self.session_key:
            self.session_key = self._make_request("get_session_key", [self.username, self.password])
        return self.session_key

    def get_responses(self, survey_id: str, completed_only: bool = False,
        start_date: Optional[datetime] = None, end_date: Optional[datetime] = None) -> str:
        """
        Stažení dat průzkumu a uložení do CSV
        
        Args:
            survey_id: ID průzkumu
            completed_only: Pouze dokončené odpovědi
            start_date: Počáteční datum
            end_date: Koncové datum
            
        Returns:
            Cesta k uloženému CSV souboru
        """
        logger.info(f"Stahuji odpovědi pro průzkum {survey_id}")
        
        # Vytvoření podadresáře pro průzkum s názvem serveru
        server_dir = os.path.join(self.data_dir, self.server_name)
        survey_dir = os.path.join(server_dir, survey_id)
        os.makedirs(survey_dir, exist_ok=True)
        
        # Příprava parametrů
        params = [
            self.get_session_key(),
            survey_id,
            'csv',
            None,  # headingType
            'complete' if completed_only else 'all',  # responseType
            'en',  # lang
            True,  # completionStatus
            start_date.strftime("%Y-%m-%d %H:%M:%S") if start_date else None,
            end_date.strftime("%Y-%m-%d %H:%M:%S") if end_date else None
        ]
        
        # Stažení dat
        data = self._make_request("export_responses", params)
        if not data:
            raise ValueError(f"Nepodařilo se stáhnout data pro průzkum {survey_id}")
            
        # Převod dat na string pokud je to dict
        if isinstance(data, dict):
            data = json.dumps(data, ensure_ascii=False)
            
        # Uložení do souboru
        csv_path = os.path.join(survey_dir, f"responses.csv")
        with open(csv_path, 'w', encoding='utf-8') as f:
            f.write(str(data))
            
        # Dekomprese a validace CSV
        from data_transformer import decompress_csv, validate_csv_structure
        decompress_csv(csv_path)
        validate_csv_structure(csv_path)
            
        logger.info(f"Data uložena do {csv_path}")
        return csv_path

    def get_survey_structure(self, survey_id: str) -> str:
        """Získání struktury průzkumu z LSS souboru"""
        server_dir = os.path.join(self.data_dir, self.server_name)
        survey_dir = os.path.join(server_dir, survey_id)
        os.makedirs(survey_dir, exist_ok=True)
        
        lss_path = os.path.join(survey_dir, "structure.lss")
        if not os.path.exists(lss_path):
            return self.get_survey_structure_via_api(survey_id)
            
        return lss_path

    def get_survey_structure_via_api(self, survey_id: str) -> dict:
        """
        Získání struktury průzkumu přes API
        
        Args:
            survey_id: ID průzkumu
            
        Returns:
            Dict se strukturou průzkumu
        """
        logger.info(f"Získávám strukturu průzkumu {survey_id} přes API")
        
        # Vytvoření podadresáře pro průzkum s názvem serveru
        server_dir = os.path.join(self.data_dir, self.server_name)
        survey_dir = os.path.join(server_dir, survey_id)
        os.makedirs(survey_dir, exist_ok=True)
        
        # Získání všech otázek
        questions = self._make_request("list_questions", [
            self.get_session_key(),
            survey_id
        ])
        
        # Získání skupin otázek
        groups = self._make_request("list_groups", [
            self.get_session_key(),
            survey_id
        ])
        
        # Sestavení struktury
        structure = {
            'survey_id': survey_id,
            'groups': [],
            'questions': [],
            'timestamp': datetime.now().isoformat()
        }
        
        # Přidání skupin
        for group in groups:
            structure['groups'].append({
                'gid': group['gid'],
                'group_name': group['group_name'],
                'questions': []
            })
            
        # Přidání otázek do skupin
        for question in questions:
            group = next((g for g in structure['groups'] if g['gid'] == question['gid']), None)
            if group:
                # Bezpečné získání odpovědí a vlastností s fallback na prázdné hodnoty
                try:
                    answers = self._get_question_answers(question['qid']) or {}
                except Exception as e:
                    logger.warning(f"Nepodařilo se získat odpovědi pro otázku {question['qid']}: {e}")
                    answers = {}
                
                # Místo get_question_properties použijeme data z list_questions
                # které už obsahují základní vlastnosti
                properties = {
                    'qid': question.get('qid'),
                    'type': question.get('type'),
                    'title': question.get('title'),
                    'question': question.get('question'),
                    'gid': question.get('gid'),
                    'mandatory': question.get('mandatory', 'N'),
                    'other': question.get('other', 'N'),
                    'help': question.get('help', ''),
                    'language': question.get('language', 'cs')
                }
                
                group['questions'].append({
                    'qid': question['qid'],
                    'title': question['title'],
                    'question': question['question'],
                    'type': question['type'],
                    'answers': answers,
                    'properties': properties
                })
        
        # Uložení struktury do souboru
        lss_path = os.path.join(survey_dir, "structure.lss")
        with open(lss_path, 'w', encoding='utf-8') as f:
            json.dump(structure, f, ensure_ascii=False, indent=2)
            
        logger.info(f"Struktura uložena do {lss_path}")
        return lss_path

    def _format_groups_to_lss(self, structure: dict) -> str:
        """Převede strukturu skupin otázek do LSS formátu"""
        if not structure or 'groups' not in structure:
            return ""
            
        lss_groups = []
        for group in structure['groups']:
            questions = []
            for question in group.get('questions', []):
                question_xml = f"""
                <question id="{question['qid']}" type="{question['type']}">
                    <title>{question['title']}</title>
                    <question_text>{question['question']}</question_text>
                    <answers>
                        {self._format_answers_to_lss(question.get('answers', {}))}
                    </answers>
                </question>"""
                questions.append(question_xml)
                
            group_xml = f"""
            <group id="{group['gid']}">
                <group_name>{group['group_name']}</group_name>
                <questions>
                    {''.join(questions)}
                </questions>
            </group>"""
            lss_groups.append(group_xml)
            
        return '\n'.join(lss_groups)
        
    def _format_answers_to_lss(self, answers: dict) -> str:
        """Převede odpovědi na LSS formát"""
        if not answers:
            return ""
            
        answer_items = []
        for code, text in answers.items():
            answer_items.append(f'<answer code="{code}">{text}</answer>')
            
        return '\n'.join(answer_items)

    def _get_question_answers(self, question_id: str) -> dict:
        """Získání možností odpovědí pro otázku"""
        answers = self._make_request("list_answers", [
            self.get_session_key(),
            question_id
        ])
        
        if not answers:
            return {}
            
        return {a['code']: a['answer'] for a in answers}
        
    def get_question_properties(self, question_id: str, properties: list = None) -> dict:
        """
        Získání vlastností otázky

        Args:
            question_id: ID otázky
            properties: Seznam požadovaných vlastností (None = všechny)

        Returns:
            Dict s vlastnostmi otázky
        """
        logger.info(f"Získávám vlastnosti otázky {question_id}")

        # Opravené parametry pro LimeSurvey API
        params = [self.get_session_key(), question_id]

        # Přidáme properties pouze pokud jsou specifikovány
        if properties:
            params.append(properties)

        return self._make_request("get_question_properties", params)

    def release_session(self):
        """Uvolnění session key"""
        if self.session_key:
            self._make_request("release_session_key", [self.session_key])
            self.session_key = None

    def get_fieldmap(self, survey_id: str) -> dict:
        """
        Získání mapování polí průzkumu
        
        Args:
            survey_id: ID průzkumu
            
        Returns:
            Dict s mapováním polí
        """
        logger.info(f"Získávám fieldmap pro průzkum {survey_id}")
        return self._make_request("get_fieldmap", [
            self.get_session_key(),
            survey_id
        ])

    def list_surveys(self) -> list:
        """
        Získání seznamu dostupných průzkumů
        
        Returns:
            List s informacemi o průzkumech
        """
        logger.info("Získávám seznam průzkumů")
        return self._make_request("list_surveys", [
            self.get_session_key()
        ]) or []
