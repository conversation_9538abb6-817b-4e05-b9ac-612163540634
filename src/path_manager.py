"""
Centr<PERSON>lní Path Manager pro správu cest k datům a grafům
Řeší všechny cesty na základě server/survey_id parametrů
"""

import os
import logging
from typing import Optional, Dict, List
from config_loader import load_config, get_available_servers

logger = logging.getLogger(__name__)

class PathManager:
    """Centrální správce cest pro data a grafy"""
    
    def __init__(self):
        """Inicializace Path Manageru"""
        self.config = load_config()
        self.current_server_key: Optional[str] = None
        self.current_survey_id: Optional[str] = None
        self._server_names_cache: Dict[str, str] = {}
        
    def set_server(self, server_key: Optional[str]) -> bool:
        """
        Nastaví aktuální server a resetuje survey_id
        
        Args:
            server_key: Klíč serveru nebo None pro default
            
        Returns:
            True pokud server existuje, False jinak
        """
        if server_key is None:
            self.current_server_key = None
            self.current_survey_id = None
            return True
            
        servers = get_available_servers()
        if server_key in servers:
            self.current_server_key = server_key
            self.current_survey_id = None  # Reset survey při změně serveru
            logger.info(f"Server nastaven na: {servers[server_key]['name']}")
            return True
        else:
            logger.error(f"Server {server_key} neexistuje v konfiguraci")
            return False
    
    def set_survey(self, survey_id: str) -> None:
        """Nastaví aktuální survey_id"""
        self.current_survey_id = survey_id
        logger.info(f"Survey nastaven na: {survey_id}")
    
    def get_server_name(self, server_key: Optional[str] = None) -> str:
        """
        Získá název serveru pro daný klíč
        
        Args:
            server_key: Klíč serveru nebo None pro aktuální
            
        Returns:
            Název serveru pro použití v cestách
        """
        if server_key is None:
            server_key = self.current_server_key
            
        if server_key is None:
            return "dotazniky.urad.online"  # default server
            
        # Cache pro rychlejší přístup
        if server_key in self._server_names_cache:
            return self._server_names_cache[server_key]
            
        servers = get_available_servers()
        if server_key in servers:
            server_name = servers[server_key]['name']
            self._server_names_cache[server_key] = server_name
            return server_name
        else:
            logger.warning(f"Server {server_key} nenalezen, používám default")
            return "dotazniky.urad.online"
    
    def get_data_path(self, survey_id: Optional[str] = None, filename: str = "", 
                     server_key: Optional[str] = None) -> str:
        """
        Vytvoří cestu k datovému souboru
        
        Args:
            survey_id: ID průzkumu nebo None pro aktuální
            filename: Název souboru nebo prázdný string pro adresář
            server_key: Klíč serveru nebo None pro aktuální
            
        Returns:
            Cesta ve formátu: data/server_name/survey_id/filename
        """
        if survey_id is None:
            survey_id = self.current_survey_id
            
        if survey_id is None:
            raise ValueError("Survey ID není nastaveno")
            
        server_name = self.get_server_name(server_key)
        
        if filename:
            return os.path.join("data", server_name, survey_id, filename)
        else:
            return os.path.join("data", server_name, survey_id)
    
    def get_charts_path(self, survey_id: Optional[str] = None, filename: str = "",
                       server_key: Optional[str] = None) -> str:
        """
        Vytvoří cestu k souboru grafů
        
        Args:
            survey_id: ID průzkumu nebo None pro aktuální
            filename: Název souboru nebo prázdný string pro adresář
            server_key: Klíč serveru nebo None pro aktuální
            
        Returns:
            Cesta ve formátu: charts/server_name/survey_id/filename
        """
        if survey_id is None:
            survey_id = self.current_survey_id
            
        if survey_id is None:
            raise ValueError("Survey ID není nastaveno")
            
        server_name = self.get_server_name(server_key)
        
        if filename:
            return os.path.join("charts", server_name, survey_id, filename)
        else:
            return os.path.join("charts", server_name, survey_id)
    
    def get_server_data_root(self, server_key: Optional[str] = None) -> str:
        """Získá root adresář pro data serveru"""
        server_name = self.get_server_name(server_key)
        return os.path.join("data", server_name)
    
    def get_server_charts_root(self, server_key: Optional[str] = None) -> str:
        """Získá root adresář pro grafy serveru"""
        server_name = self.get_server_name(server_key)
        return os.path.join("charts", server_name)
    
    def ensure_directories(self, survey_id: Optional[str] = None, 
                          server_key: Optional[str] = None) -> None:
        """
        Zajistí existenci všech potřebných adresářů
        
        Args:
            survey_id: ID průzkumu nebo None pro aktuální
            server_key: Klíč serveru nebo None pro aktuální
        """
        try:
            # Vytvoření data adresáře
            data_path = self.get_data_path(survey_id, server_key=server_key)
            os.makedirs(data_path, exist_ok=True)
            logger.debug(f"Vytvořen data adresář: {data_path}")
            
            # Vytvoření charts adresáře
            charts_path = self.get_charts_path(survey_id, server_key=server_key)
            os.makedirs(charts_path, exist_ok=True)
            logger.debug(f"Vytvořen charts adresář: {charts_path}")
            
        except Exception as e:
            logger.error(f"Chyba při vytváření adresářů: {str(e)}")
            raise
    
    def list_available_surveys(self, server_key: Optional[str] = None) -> List[str]:
        """
        Vrátí seznam dostupných průzkumů pro server
        
        Args:
            server_key: Klíč serveru nebo None pro aktuální
            
        Returns:
            Seznam ID průzkumů
        """
        server_data_root = self.get_server_data_root(server_key)
        
        if not os.path.exists(server_data_root):
            return []
            
        surveys = []
        for item in os.listdir(server_data_root):
            item_path = os.path.join(server_data_root, item)
            if os.path.isdir(item_path) and item.isdigit():
                surveys.append(item)
                
        return sorted(surveys)
    
    def validate_survey_data(self, survey_id: Optional[str] = None,
                           server_key: Optional[str] = None) -> Dict[str, bool]:
        """
        Zkontroluje existenci klíčových souborů průzkumu
        
        Returns:
            Dict s informacemi o existenci souborů
        """
        result = {
            'responses_csv': False,
            'structure_lss': False,
            'question_mapping': False,
            'responses_long': False,
            'chart_data': False
        }
        
        try:
            files_to_check = {
                'responses_csv': 'responses.csv',
                'structure_lss': 'structure.lss', 
                'question_mapping': 'question_mapping.csv',
                'responses_long': 'responses_long.csv',
                'chart_data': 'chart_data.json'
            }
            
            for key, filename in files_to_check.items():
                file_path = self.get_data_path(survey_id, filename, server_key)
                result[key] = os.path.exists(file_path)
                
        except Exception as e:
            logger.error(f"Chyba při validaci dat průzkumu: {str(e)}")
            
        return result
    
    def get_current_state(self) -> Dict[str, Optional[str]]:
        """Vrátí aktuální stav manageru"""
        return {
            'server_key': self.current_server_key,
            'server_name': self.get_server_name() if self.current_server_key else None,
            'survey_id': self.current_survey_id
        }

# Globální instance pro sdílení mezi moduly
path_manager = PathManager()
