"""
Wrapper pro LimeSurveyClient s podporou PathManageru a více serverů
"""

import os
import logging
from typing import Optional, List, Dict, Any
from limesurvey_client import LimeSurveyClient
from path_manager import path_manager
from config_loader import get_available_servers

logger = logging.getLogger(__name__)

class LimeSurveyWrapper:
    """Wrapper pro LimeSurveyClient s podporou více serverů"""
    
    def __init__(self, server_key: Optional[str] = None):
        """
        Inicializace wrapperu
        
        Args:
            server_key: Klíč serveru nebo None pro default
        """
        self.server_key = server_key
        self.server_name = self._get_server_name()
        self.client = self._create_client()
        
        # Nastavení PathManageru
        path_manager.set_server(server_key)
        
    def _get_server_name(self) -> str:
        """Získá název serveru"""
        if self.server_key:
            servers = get_available_servers()
            if self.server_key in servers:
                return servers[self.server_key]['name']
        return "dotazniky.urad.online"
    
    def _create_client(self) -> LimeSurveyClient:
        """Vytvoří LimeSurveyClient s správnou konfigurací"""
        if self.server_key:
            # Pro specifický server - musíme dočasně změnit konfiguraci
            servers = get_available_servers()
            if self.server_key in servers:
                server_config = servers[self.server_key]
                
                # Dočasně změníme environment variables
                original_url = os.environ.get('LIMESURVEY_API_URL')
                original_username = os.environ.get('LIMESURVEY_USERNAME')
                original_password = os.environ.get('LIMESURVEY_PASSWORD')
                
                os.environ['LIMESURVEY_API_URL'] = server_config['API_URL']
                os.environ['LIMESURVEY_USERNAME'] = server_config['USERNAME']
                os.environ['LIMESURVEY_PASSWORD'] = server_config['PASSWORD']
                
                try:
                    client = LimeSurveyClient()
                    client.server_name = self.server_name
                    client.server_key = self.server_key
                    return client
                finally:
                    # Obnovíme původní hodnoty
                    if original_url:
                        os.environ['LIMESURVEY_API_URL'] = original_url
                    if original_username:
                        os.environ['LIMESURVEY_USERNAME'] = original_username
                    if original_password:
                        os.environ['LIMESURVEY_PASSWORD'] = original_password
        
        # Default server
        client = LimeSurveyClient()
        client.server_name = self.server_name
        client.server_key = "default"
        return client
    
    def list_surveys(self) -> Optional[List[Dict[str, Any]]]:
        """Získání seznamu průzkumů"""
        return self.client.list_surveys()
    
    def get_responses(self, survey_id: str) -> str:
        """
        Stažení odpovědí průzkumu

        Args:
            survey_id: ID průzkumu

        Returns:
            Cesta k staženému CSV souboru
        """
        # Zajistíme existenci adresářů
        path_manager.ensure_directories(survey_id, self.server_key)

        try:
            # Stáhneme data pomocí původního klienta
            temp_path = self.client.get_responses(survey_id)

            if not temp_path or not os.path.exists(temp_path):
                raise Exception(f"Stažení dat selhalo - soubor {temp_path} neexistuje")

            # Přesuneme do správného umístění podle PathManageru
            target_path = path_manager.get_data_path(survey_id, "responses.csv", self.server_key)

            if temp_path != target_path:
                # Zajistíme existenci cílového adresáře
                os.makedirs(os.path.dirname(target_path), exist_ok=True)

                # Přesuneme soubor
                if os.path.exists(temp_path):
                    import shutil
                    shutil.move(temp_path, target_path)
                    logger.info(f"Soubor přesunut z {temp_path} do {target_path}")

            return target_path

        except Exception as e:
            logger.error(f"Chyba při stahování odpovědí: {str(e)}")
            raise
    
    def get_survey_structure(self, survey_id: str) -> str:
        """
        Stažení struktury průzkumu

        Args:
            survey_id: ID průzkumu

        Returns:
            Cesta k staženému LSS souboru
        """
        # Zajistíme existenci adresářů
        path_manager.ensure_directories(survey_id, self.server_key)

        # Nejprve zkontrolujeme, zda soubor už existuje v novém umístění
        target_path = path_manager.get_data_path(survey_id, "structure.lss", self.server_key)

        if os.path.exists(target_path):
            logger.info(f"LSS soubor už existuje: {target_path}")
            return target_path

        # Zkontrolujeme také staré umístění a přesuneme ho
        old_path = f"data/{survey_id}/structure.lss"
        if os.path.exists(old_path):
            logger.info(f"Nalezen LSS soubor ve starém umístění: {old_path}")
            os.makedirs(os.path.dirname(target_path), exist_ok=True)
            import shutil
            shutil.move(old_path, target_path)
            logger.info(f"LSS soubor přesunut z {old_path} do {target_path}")
            return target_path

        try:
            # Stáhneme strukturu pomocí původního klienta
            temp_path = self.client.get_survey_structure(survey_id)

            if not temp_path or not os.path.exists(temp_path):
                raise Exception(f"Stažení struktury selhalo - soubor {temp_path} neexistuje")

            if temp_path != target_path:
                # Zajistíme existenci cílového adresáře
                os.makedirs(os.path.dirname(target_path), exist_ok=True)

                # Přesuneme soubor
                if os.path.exists(temp_path):
                    import shutil
                    shutil.move(temp_path, target_path)
                    logger.info(f"Soubor přesunut z {temp_path} do {target_path}")

            return target_path

        except Exception as e:
            logger.error(f"Chyba při stahování struktury: {str(e)}")
            # Pokud stahování selhalo, ale soubor existuje v cílovém umístění, použijeme ho
            if os.path.exists(target_path):
                logger.info(f"Používám existující LSS soubor: {target_path}")
                return target_path
            raise
    
    def get_server_info(self) -> Dict[str, str]:
        """Vrátí informace o aktuálním serveru"""
        return {
            'server_key': self.server_key or 'default',
            'server_name': self.server_name,
            'api_url': self.client.api_url if hasattr(self.client, 'api_url') else 'unknown'
        }

def create_limesurvey_client(server_key: Optional[str] = None) -> LimeSurveyWrapper:
    """
    Factory funkce pro vytvoření LimeSurvey klienta
    
    Args:
        server_key: Klíč serveru nebo None pro default
        
    Returns:
        LimeSurveyWrapper instance
    """
    return LimeSurveyWrapper(server_key)
