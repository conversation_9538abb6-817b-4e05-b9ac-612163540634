# Okamžité kroky k implementaci

## Kroky k realizaci nyní (leden 2025)

Na základě analýzy navrhovaných rozšíření a aktuálního stavu aplikace doporučujem následující okamžité kroky:

### 1. Konsolidace dokumentace (1-2 dny)
**Priorita: Vysoká**

#### Co udělat:
- [x] Vytvořit strukturovaný plán rozšíření (06_plan_rozsireni.md)
- [ ] Aktualizovat README.md s aktuálními funkcemi
- [ ] Vytvořit uživatelskou dokumentaci pro CLI
- [ ] Dokumentovat API rozhraní pro Datawrapper

#### Důvod:
Před dalším vývojem je nutné mít jasnou dokumentaci toho, co aplikace umí a kam směřuje.

### 2. Stabilizace základní funkcionality (3-5 dní)
**Priorita: Vysoká**

#### Co udělat:
- [ ] Dokončit opravy v CLI menu (EOFError handling)
- [ ] Ověřit funkčnost všech základních operací
- [ ] Rozšířit unit testy pro kritické funkce
- [ ] Optimalizovat výkon při zpracování velkých průzkumů

#### Důvod:
Před přidáváním nových funkcí musí být základní funkcionalita 100% spolehlivá.

### 3. Příprava infrastruktury pro rozšíření (2-3 dny)
**Priorita: Střední**

#### Co udělat:
- [ ] Refaktorovat konfigurační systém pro budoucí rozšíření
- [ ] Připravit modulární strukturu pro nové typy grafů
- [ ] Vytvořit abstraktní třídy pro AI funkce (i když zatím prázdné)
- [ ] Nastavit logging pro lepší debugging

#### Důvod:
Usnadní budoucí implementaci rozšíření a zabrání velkým refaktoringům.

## Kroky k odložení na později

### Odložit na Fázi 2 (Q2 2025):
- **Podpora více LimeSurvey serverů** - vyžaduje významné změny v architektuře
- **Rozšířená metadata** - potřebuje stabilní základ pro testování
- **AI funkce** - vyžadují dodatečné závislosti a komplexní testování

### Odložit na Fázi 3+ (Q3 2025 a později):
- **Nové typy grafů** - nejprve stabilizovat stávající
- **GUI rozšíření** - vyžaduje značné zdroje
- **Speciální analýzy** - specializované funkce pro pokročilé uživatele

## Konkrétní implementační kroky pro tento týden

### Den 1-2: Dokumentace
1. Aktualizovat README.md
2. Vytvořit user guide pro CLI
3. Zdokumentovat všechny menu položky

### Den 3-4: Stabilizace
1. Opravit všechny známé bugy v CLI
2. Rozšířit testy
3. Ověřit funkčnost na různých průzkumech

### Den 5: Příprava budoucnosti
1. Refaktorovat config_loader.py pro rozšiřitelnost
2. Připravit skeleton pro AI moduly
3. Nastavit lepší logging

## Kritéria pro přechod k dalším fázím

### Před Fází 2:
- [ ] Všechny základní funkce fungují bez chyb
- [ ] Dokumentace je kompletní a aktuální
- [ ] Unit testy pokrývají alespoň 80% kódu
- [ ] Aplikace je testována na alespoň 5 různých průzkumech

### Před implementací AI funkcí:
- [ ] Stabilní API pro předávání dat do AI
- [ ] Bezpečnostní opatření pro citlivá data
- [ ] Testovací framework pro AI výstupy
- [ ] Uživatelské rozhraní pro správu promptů

## Doporučení pro harmonogram

### Leden 2025: Konsolidace
- Dokončit všechny okamžité kroky
- Připravit stabilní verzi 1.0

### Únor-Březen 2025: Plánování Fáze 2
- Detailní analýza požadavků na více serverů
- Návrh architektury pro metadata
- Prototypy nových funkcí

### Duben+ 2025: Implementace podle plánu
- Postupná implementace podle priorit
- Pravidelné testování a feedback

## Poznámky

### Rizika:
- Příliš rychlé přidávání funkcí může destabilizovat aplikaci
- AI funkce vyžadují pečlivé testování kvůli neprediktabilním výstupům
- GUI implementace je časově náročná

### Příležitosti:
- Modulární architektura umožní snadné přidávání funkcí
- AI funkce mohou výrazně zlepšit uživatelský zážitek
- Podpora více serverů rozšíří použitelnost

### Doporučení:
- Držet se konzervativního přístupu
- Každou novou funkci důkladně otestovat
- Pravidelně sbírat feedback od uživatelů
- Udržovat zpětnou kompatibilitu
